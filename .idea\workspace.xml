<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="f20623a5-ca33-47ec-9c06-46a03bd57795" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="30U5d11RfOXsbT4BjvH4KCg3Ogx" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;Python.main.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;
  }
}</component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-PY-251.26927.90" />
        <option value="bundled-python-sdk-41e8cd69c857-64d779b69b7a-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-251.26927.90" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="f20623a5-ca33-47ec-9c06-46a03bd57795" name="更改" comment="" />
      <created>1753669394976</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753669394976</updated>
      <workItem from="1753669396458" duration="30000" />
      <workItem from="1753669453265" duration="594000" />
      <workItem from="1753772871771" duration="1090000" />
      <workItem from="1753843426824" duration="3641000" />
      <workItem from="1753925064274" duration="154000" />
      <workItem from="1753957051531" duration="1212000" />
      <workItem from="1754017567465" duration="113000" />
      <workItem from="1754017701450" duration="998000" />
      <workItem from="1754018845196" duration="78000" />
      <workItem from="1754019495172" duration="520000" />
      <workItem from="1754020037993" duration="128000" />
      <workItem from="1754020220136" duration="22000" />
      <workItem from="1754020280994" duration="236000" />
      <workItem from="1754020564808" duration="163000" />
      <workItem from="1754021756383" duration="39000" />
      <workItem from="1754022009562" duration="82000" />
      <workItem from="1754022962645" duration="289000" />
      <workItem from="1754023306075" duration="27000" />
      <workItem from="1754023664550" duration="119000" />
      <workItem from="1754031861414" duration="80000" />
      <workItem from="1754297427399" duration="2458000" />
      <workItem from="1754383915533" duration="4781000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/TeleTest_bak$main.coverage" NAME="main 覆盖结果" MODIFIED="1753670013775" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>